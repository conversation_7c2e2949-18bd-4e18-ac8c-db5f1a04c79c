{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to initialize Firebase Admin SDK:\u001b[39m","timestamp":"2025-06-20 10:46:50:4650"}
{"code":"ECONNREFUSED","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase connection failed:\u001b[39m","stack":"AggregateError [ECONNREFUSED]: \n    at K:\\AI\\PromoTun\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDatabase (K:\\AI\\PromoTun\\backend\\src\\database\\connection.js:22:20)\n    at async startServer (K:\\AI\\PromoTun\\backend\\src\\server.js:114:5)","timestamp":"2025-06-20 10:46:50:4650"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to initialize Firebase Admin SDK:\u001b[39m","timestamp":"2025-06-20 10:48:22:4822"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mGet promotions error: Cannot read properties of undefined (reading 'count')\u001b[39m","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at K:\\AI\\PromoTun\\backend\\src\\routes\\promotions.js:131:48\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-20 10:50:36:5036"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to initialize Firebase Admin SDK:\u001b[39m","timestamp":"2025-06-20 10:51:42:5142"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mEmail sending failed: nodemailer.createTransporter is not a function\u001b[39m","stack":"TypeError: nodemailer.createTransporter is not a function\n    at createTransporter (K:\\AI\\PromoTun\\backend\\src\\services\\emailService.js:17:23)\n    at sendEmail (K:\\AI\\PromoTun\\backend\\src\\services\\emailService.js:33:25)\n    at K:\\AI\\PromoTun\\backend\\src\\routes\\auth.js:75:11","timestamp":"2025-06-20 10:51:53:5153"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRegistration error: nodemailer.createTransporter is not a function\u001b[39m","stack":"TypeError: nodemailer.createTransporter is not a function\n    at createTransporter (K:\\AI\\PromoTun\\backend\\src\\services\\emailService.js:17:23)\n    at sendEmail (K:\\AI\\PromoTun\\backend\\src\\services\\emailService.js:33:25)\n    at K:\\AI\\PromoTun\\backend\\src\\routes\\auth.js:75:11","timestamp":"2025-06-20 10:51:53:5153"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError: Unexpected token '\"', \"\"invalid json\"\" is not valid JSON\u001b[39m","method":"POST","stack":"SyntaxError: Unexpected token '\"', \"\"invalid json\"\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (K:\\AI\\PromoTun\\backend\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (K:\\AI\\PromoTun\\backend\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at K:\\AI\\PromoTun\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (K:\\AI\\PromoTun\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (K:\\AI\\PromoTun\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (K:\\AI\\PromoTun\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)","timestamp":"2025-06-20 10:52:53:5253","url":"/api/auth/login"}
