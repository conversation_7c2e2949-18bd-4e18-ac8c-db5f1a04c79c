#!/usr/bin/env node

const { spawn } = require('child_process');

console.log('🐳 Docker Availability Check');
console.log('============================\n');

// Utility function to run commands
function runCommand(command, args) {
  return new Promise((resolve, reject) => {
    const process = spawn(command, args, {
      stdio: 'pipe',
      shell: true
    });

    let stdout = '';
    let stderr = '';

    process.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    process.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    process.on('close', (code) => {
      resolve({ code, stdout, stderr });
    });

    process.on('error', (error) => {
      reject({ error: error.message });
    });
  });
}

async function checkDocker() {
  console.log('🔍 Checking Docker installation...\n');
  
  try {
    // Check Docker version
    const dockerVersion = await runCommand('docker', ['--version']);
    if (dockerVersion.code === 0) {
      console.log('✅ Docker is installed');
      console.log(`   ${dockerVersion.stdout.trim()}`);
    } else {
      console.log('❌ Docker is not installed');
      return false;
    }
    
    // Check Docker Compose
    const composeVersion = await runCommand('docker-compose', ['--version']);
    if (composeVersion.code === 0) {
      console.log('✅ Docker Compose is available');
      console.log(`   ${composeVersion.stdout.trim()}`);
    } else {
      console.log('❌ Docker Compose is not available');
    }
    
    // Check if Docker daemon is running
    console.log('\n🔍 Checking Docker daemon...');
    const dockerInfo = await runCommand('docker', ['info']);
    
    if (dockerInfo.code === 0) {
      console.log('✅ Docker daemon is running');
      console.log('✅ Docker is ready for use!\n');
      
      // Show Docker system info
      const dockerPs = await runCommand('docker', ['ps']);
      if (dockerPs.stdout.trim()) {
        console.log('📊 Running containers:');
        console.log(dockerPs.stdout);
      } else {
        console.log('📊 No containers currently running');
      }
      
      return true;
    } else {
      console.log('❌ Docker daemon is not running');
      console.log('   Error:', dockerInfo.stderr.trim());
      return false;
    }
    
  } catch (error) {
    console.log('❌ Error checking Docker:', error.error || error.message);
    return false;
  }
}

async function main() {
  const dockerReady = await checkDocker();
  
  if (dockerReady) {
    console.log('\n🎉 Docker is ready! You can now run:');
    console.log('=====================================');
    console.log('🐳 Full Docker deployment: node deploy-docker.js');
    console.log('🔧 Simple Docker setup: node setup-docker-environment.js');
    console.log('📋 Manual deployment: cd deployment && docker-compose up -d');
    
    console.log('\n📚 Available Docker Compose files:');
    console.log('==================================');
    console.log('📄 deployment/docker-compose.yml - Full production stack');
    console.log('📄 deployment/docker-compose-simple.yml - Database + Redis only');
    
  } else {
    console.log('\n💡 To use Docker with PromoTun:');
    console.log('===============================');
    console.log('1. 📥 Install Docker Desktop:');
    console.log('   https://www.docker.com/products/docker-desktop');
    console.log('');
    console.log('2. 🚀 Start Docker Desktop application');
    console.log('');
    console.log('3. ✅ Verify installation:');
    console.log('   docker --version');
    console.log('   docker info');
    console.log('');
    console.log('4. 🔄 Run this check again:');
    console.log('   node check-docker.js');
    
    console.log('\n🔄 Alternative: Continue with mock services');
    console.log('===========================================');
    console.log('If you prefer to continue without Docker:');
    console.log('1. 🚀 Start backend: cd backend && npm start');
    console.log('2. 🧪 Run tests: node test-runner.js');
    console.log('3. 🔗 Test API: node test-api.js');
    console.log('');
    console.log('The backend will use mock database and Redis services.');
  }
  
  console.log('\n📖 For more information, see:');
  console.log('=============================');
  console.log('📄 QUICK_START_GUIDE.md');
  console.log('📄 DEPLOYMENT_GUIDE.md');
  console.log('📄 TESTING_DEPLOYMENT_REPORT.md');
}

main().catch(error => {
  console.error('💥 Check failed:', error.message);
  process.exit(1);
});
