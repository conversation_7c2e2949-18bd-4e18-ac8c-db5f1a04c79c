#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🐳 PromoTun Docker Environment Setup');
console.log('====================================\n');

// Check if Docker is available and running
async function checkDockerStatus() {
  console.log('🔍 Checking Docker Status...\n');
  
  try {
    // Check if Docker is installed
    const dockerVersion = await runCommand('docker', ['--version'], process.cwd(), { silent: true });
    console.log('✅ Docker is installed');
    console.log(`   Version: ${dockerVersion.stdout.trim()}`);
    
    // Check if Docker daemon is running
    try {
      await runCommand('docker', ['info'], process.cwd(), { silent: true });
      console.log('✅ Docker daemon is running');
      return true;
    } catch (error) {
      console.log('❌ Docker daemon is not running');
      console.log('   Please start Docker Desktop and try again');
      return false;
    }
  } catch (error) {
    console.log('❌ Docker is not installed or not in PATH');
    console.log('   Please install Docker Desktop from https://www.docker.com/products/docker-desktop');
    return false;
  }
}

// Utility function to run commands
function runCommand(command, args, cwd, options = {}) {
  return new Promise((resolve, reject) => {
    if (!options.silent) {
      console.log(`📋 Running: ${command} ${args.join(' ')}`);
      console.log(`📁 Directory: ${cwd}\n`);
    }
    
    const process = spawn(command, args, {
      cwd,
      stdio: options.silent ? 'pipe' : 'inherit',
      shell: true,
      ...options
    });

    let stdout = '';
    let stderr = '';

    if (options.silent) {
      process.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      process.stderr.on('data', (data) => {
        stderr += data.toString();
      });
    }

    process.on('close', (code) => {
      if (code === 0) {
        resolve({ code, stdout, stderr });
      } else {
        reject({ code, stdout, stderr });
      }
    });

    process.on('error', (error) => {
      reject({ error: error.message, code: -1 });
    });
  });
}

// Create a simplified Docker Compose for testing
async function createSimpleDockerCompose() {
  console.log('📝 Creating simplified Docker Compose configuration...\n');
  
  const simpleCompose = `version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: promotun-postgres
    environment:
      POSTGRES_DB: promotun
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: SecurePromoTunPass123!
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/01-init-db.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d promotun"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: promotun-redis
    command: redis-server --appendonly yes --requirepass SecureRedisPass123!
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "SecureRedisPass123!", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    name: promotun-network`;

  const deploymentPath = path.join(process.cwd(), 'deployment');
  const simpleComposePath = path.join(deploymentPath, 'docker-compose-simple.yml');
  
  fs.writeFileSync(simpleComposePath, simpleCompose);
  console.log('✅ Created simplified Docker Compose file');
  console.log(`   Location: ${simpleComposePath}\n`);
  
  return simpleComposePath;
}

// Test Docker with simple services
async function testDockerWithSimpleServices() {
  console.log('🧪 Testing Docker with PostgreSQL and Redis...\n');
  
  try {
    const deploymentPath = path.join(process.cwd(), 'deployment');
    const simpleComposePath = await createSimpleDockerCompose();
    
    // Start simple services
    console.log('🚀 Starting PostgreSQL and Redis containers...');
    await runCommand('docker-compose', ['-f', 'docker-compose-simple.yml', 'up', '-d'], deploymentPath);
    
    // Wait for services to be ready
    console.log('⏳ Waiting for services to be ready...');
    await new Promise(resolve => setTimeout(resolve, 30000)); // Wait 30 seconds
    
    // Check service status
    const status = await runCommand('docker-compose', ['-f', 'docker-compose-simple.yml', 'ps'], deploymentPath, { silent: true });
    console.log('📊 Service Status:');
    console.log(status.stdout);
    
    // Test PostgreSQL connection
    try {
      await runCommand('docker', ['exec', 'promotun-postgres', 'pg_isready', '-U', 'postgres'], process.cwd(), { silent: true });
      console.log('✅ PostgreSQL: Ready and accepting connections');
    } catch (error) {
      console.log('❌ PostgreSQL: Not ready');
    }
    
    // Test Redis connection
    try {
      await runCommand('docker', ['exec', 'promotun-redis', 'redis-cli', '--no-auth-warning', '-a', 'SecureRedisPass123!', 'ping'], process.cwd(), { silent: true });
      console.log('✅ Redis: Ready and accepting connections');
    } catch (error) {
      console.log('❌ Redis: Not ready');
    }
    
    console.log('\n✅ Docker services test completed successfully!');
    return true;
    
  } catch (error) {
    console.log('❌ Docker services test failed:', error.message);
    return false;
  }
}

// Update backend configuration for Docker
async function updateBackendForDocker() {
  console.log('🔧 Updating backend configuration for Docker...\n');
  
  try {
    // Update the backend .env file to disable mocks
    const backendEnvPath = path.join(process.cwd(), 'backend', '.env');
    let envContent = fs.readFileSync(backendEnvPath, 'utf8');
    
    // Update database and Redis hosts
    envContent = envContent.replace(/DB_HOST=localhost/g, 'DB_HOST=localhost');
    envContent = envContent.replace(/REDIS_HOST=localhost/g, 'REDIS_HOST=localhost');
    envContent = envContent.replace(/MOCK_DATABASE=true/g, 'MOCK_DATABASE=false');
    envContent = envContent.replace(/MOCK_EXTERNAL_APIS=false/g, 'MOCK_EXTERNAL_APIS=false');
    
    // Update passwords to match Docker
    envContent = envContent.replace(/DB_PASSWORD=password/g, 'DB_PASSWORD=SecurePromoTunPass123!');
    envContent = envContent.replace(/REDIS_PASSWORD=/g, 'REDIS_PASSWORD=SecureRedisPass123!');
    
    fs.writeFileSync(backendEnvPath, envContent);
    console.log('✅ Updated backend .env configuration');
    
    console.log('\n📋 Backend Configuration:');
    console.log('=========================');
    console.log('🗄️ Database: PostgreSQL on localhost:5432');
    console.log('🔄 Cache: Redis on localhost:6379');
    console.log('🔒 Mock Services: Disabled');
    console.log('🌐 API: Will run on localhost:5000\n');
    
    return true;
  } catch (error) {
    console.log('❌ Failed to update backend configuration:', error.message);
    return false;
  }
}

// Start backend with Docker services
async function startBackendWithDocker() {
  console.log('🚀 Starting backend with Docker services...\n');
  
  try {
    const backendPath = path.join(process.cwd(), 'backend');
    
    // Install dependencies if needed
    if (!fs.existsSync(path.join(backendPath, 'node_modules'))) {
      console.log('📦 Installing backend dependencies...');
      await runCommand('npm', ['install'], backendPath);
    }
    
    console.log('🔧 Starting backend server...');
    console.log('   The backend will connect to Docker PostgreSQL and Redis');
    console.log('   Press Ctrl+C to stop the backend server\n');
    
    // Start backend (this will run in foreground)
    await runCommand('npm', ['start'], backendPath);
    
  } catch (error) {
    console.log('❌ Failed to start backend:', error.message);
    return false;
  }
}

// Generate instructions
function generateInstructions() {
  console.log('📖 PromoTun Docker Environment Instructions');
  console.log('===========================================\n');
  
  console.log('🎯 Current Status:');
  console.log('✅ PostgreSQL: Running in Docker (localhost:5432)');
  console.log('✅ Redis: Running in Docker (localhost:6379)');
  console.log('✅ Backend: Ready to connect to Docker services');
  
  console.log('\n🚀 To start the complete application:');
  console.log('=====================================');
  console.log('1. Keep Docker services running (PostgreSQL & Redis)');
  console.log('2. Start backend: cd backend && npm start');
  console.log('3. Test API: curl http://localhost:5000/health');
  console.log('4. Start mobile app: cd mobile-app && npm start');
  console.log('5. Start merchant portal: cd merchant-portal && npm run dev');
  
  console.log('\n🐳 Docker Management Commands:');
  console.log('==============================');
  console.log('📊 Status: docker-compose -f deployment/docker-compose-simple.yml ps');
  console.log('📋 Logs: docker-compose -f deployment/docker-compose-simple.yml logs -f');
  console.log('🛑 Stop: docker-compose -f deployment/docker-compose-simple.yml down');
  console.log('🔄 Restart: docker-compose -f deployment/docker-compose-simple.yml restart');
  
  console.log('\n🔑 Database Connection Details:');
  console.log('===============================');
  console.log('🗄️ PostgreSQL:');
  console.log('   Host: localhost');
  console.log('   Port: 5432');
  console.log('   Database: promotun');
  console.log('   Username: postgres');
  console.log('   Password: SecurePromoTunPass123!');
  
  console.log('\n🔄 Redis:');
  console.log('   Host: localhost');
  console.log('   Port: 6379');
  console.log('   Password: SecureRedisPass123!');
  
  console.log('\n🎉 Docker environment is ready for development!');
}

// Main function
async function setupDockerEnvironment() {
  try {
    const dockerAvailable = await checkDockerStatus();
    
    if (!dockerAvailable) {
      console.log('\n💡 To use Docker with PromoTun:');
      console.log('1. Install Docker Desktop from https://www.docker.com/products/docker-desktop');
      console.log('2. Start Docker Desktop');
      console.log('3. Run this script again');
      console.log('\n🔄 Alternatively, you can continue with the mock services for development.');
      return;
    }
    
    console.log('\n🎯 Docker is available! Setting up PromoTun environment...\n');
    
    const servicesReady = await testDockerWithSimpleServices();
    
    if (servicesReady) {
      await updateBackendForDocker();
      generateInstructions();
      
      console.log('\n🚀 Would you like to start the backend now? (Ctrl+C to exit)');
      console.log('   Starting in 5 seconds...\n');
      
      await new Promise(resolve => setTimeout(resolve, 5000));
      await startBackendWithDocker();
    } else {
      console.log('\n❌ Docker services failed to start properly.');
      console.log('🔧 Please check Docker Desktop and try again.');
    }
    
  } catch (error) {
    console.error('💥 Setup failed:', error.message);
    process.exit(1);
  }
}

// Run the setup
setupDockerEnvironment();
