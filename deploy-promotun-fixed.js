#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const http = require('http');

console.log('🚀 PromoTun Fixed Docker Deployment');
console.log('===================================\n');

// Deployment status
const status = {
  dockerCheck: false,
  cleanup: false,
  build: false,
  deploy: false,
  verify: false
};

// Utility function to run commands
function runCommand(command, args, cwd = process.cwd(), options = {}) {
  return new Promise((resolve, reject) => {
    if (!options.silent) {
      console.log(`📋 ${command} ${args.join(' ')}`);
    }
    
    const process = spawn(command, args, {
      cwd,
      stdio: options.silent ? 'pipe' : 'inherit',
      shell: true
    });

    let stdout = '';
    let stderr = '';

    if (options.silent) {
      process.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      process.stderr.on('data', (data) => {
        stderr += data.toString();
      });
    }

    process.on('close', (code) => {
      if (code === 0) {
        resolve({ code, stdout, stderr });
      } else {
        reject({ code, stdout, stderr });
      }
    });

    process.on('error', (error) => {
      reject({ error: error.message });
    });
  });
}

// Test HTTP endpoint
function testEndpoint(url, timeout = 10000) {
  return new Promise((resolve) => {
    const request = http.get(url, { timeout }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          success: true,
          status: res.statusCode,
          data: data.substring(0, 200)
        });
      });
    });

    request.on('error', (error) => {
      resolve({
        success: false,
        error: error.message
      });
    });

    request.on('timeout', () => {
      request.destroy();
      resolve({
        success: false,
        error: 'Request timeout'
      });
    });
  });
}

// Check Docker
async function checkDocker() {
  console.log('🐳 Checking Docker...\n');
  
  try {
    await runCommand('docker', ['--version'], process.cwd(), { silent: true });
    console.log('✅ Docker installed');
    
    await runCommand('docker', ['info'], process.cwd(), { silent: true });
    console.log('✅ Docker daemon running');
    
    status.dockerCheck = true;
    return true;
  } catch (error) {
    console.log('❌ Docker not available');
    console.log('💡 Please start Docker Desktop and try again');
    return false;
  }
}

// Cleanup existing deployment
async function cleanup() {
  console.log('\n🧹 Cleaning up existing deployment...\n');
  
  try {
    const deploymentPath = path.join(process.cwd(), 'deployment');
    
    // Stop existing containers
    try {
      await runCommand('docker-compose', ['-f', 'docker-compose-core.yml', 'down', '-v'], deploymentPath);
      console.log('✅ Stopped existing containers');
    } catch (error) {
      console.log('ℹ️ No existing containers to stop');
    }
    
    // Clean up Docker system
    try {
      await runCommand('docker', ['system', 'prune', '-f'], process.cwd(), { silent: true });
      console.log('✅ Cleaned up Docker system');
    } catch (error) {
      console.log('⚠️ Could not clean up Docker system');
    }
    
    status.cleanup = true;
    return true;
  } catch (error) {
    console.log('❌ Cleanup failed:', error.message);
    return false;
  }
}

// Build images
async function buildImages() {
  console.log('\n🔨 Building Docker images...\n');
  
  try {
    const deploymentPath = path.join(process.cwd(), 'deployment');
    
    // Build images one by one for better error handling
    console.log('🔧 Building backend image...');
    await runCommand('docker-compose', ['-f', 'docker-compose-core.yml', 'build', 'backend'], deploymentPath);
    
    console.log('🔧 Building merchant portal image...');
    await runCommand('docker-compose', ['-f', 'docker-compose-core.yml', 'build', 'merchant-portal'], deploymentPath);
    
    console.log('🔧 Building admin dashboard image...');
    await runCommand('docker-compose', ['-f', 'docker-compose-core.yml', 'build', 'admin-dashboard'], deploymentPath);
    
    console.log('✅ All images built successfully');
    status.build = true;
    return true;
  } catch (error) {
    console.log('❌ Build failed:', error.message);
    return false;
  }
}

// Deploy services
async function deployServices() {
  console.log('\n🚀 Deploying services...\n');
  
  try {
    const deploymentPath = path.join(process.cwd(), 'deployment');
    
    // Start database services first
    console.log('🗄️ Starting database services...');
    await runCommand('docker-compose', ['-f', 'docker-compose-core.yml', 'up', '-d', 'postgres', 'redis'], deploymentPath);
    
    console.log('⏳ Waiting for database services (30s)...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
    // Start backend
    console.log('🔧 Starting backend service...');
    await runCommand('docker-compose', ['-f', 'docker-compose-core.yml', 'up', '-d', 'backend'], deploymentPath);
    
    console.log('⏳ Waiting for backend service (30s)...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
    // Start web services
    console.log('🌐 Starting web services...');
    await runCommand('docker-compose', ['-f', 'docker-compose-core.yml', 'up', '-d', 'merchant-portal', 'admin-dashboard'], deploymentPath);
    
    console.log('⏳ Waiting for web services (30s)...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
    // Start nginx
    console.log('🔀 Starting nginx proxy...');
    await runCommand('docker-compose', ['-f', 'docker-compose-core.yml', 'up', '-d', 'nginx'], deploymentPath);
    
    console.log('⏳ Final startup wait (15s)...');
    await new Promise(resolve => setTimeout(resolve, 15000));
    
    status.deploy = true;
    return true;
  } catch (error) {
    console.log('❌ Deployment failed:', error.message);
    return false;
  }
}

// Verify deployment
async function verifyDeployment() {
  console.log('\n🔍 Verifying deployment...\n');
  
  try {
    const deploymentPath = path.join(process.cwd(), 'deployment');
    
    // Check container status
    console.log('📊 Container Status:');
    const psResult = await runCommand('docker-compose', ['-f', 'docker-compose-core.yml', 'ps'], deploymentPath, { silent: true });
    console.log(psResult.stdout);
    
    // Test endpoints
    const endpoints = [
      { name: 'Backend API Health', url: 'http://localhost:5000/health' },
      { name: 'Backend API Categories', url: 'http://localhost:5000/api/categories' },
      { name: 'Merchant Portal', url: 'http://localhost:3000' },
      { name: 'Admin Dashboard', url: 'http://localhost:3001' },
      { name: 'Nginx Proxy', url: 'http://localhost' }
    ];
    
    console.log('\n🏥 Testing Endpoints:');
    let successCount = 0;
    
    for (const endpoint of endpoints) {
      console.log(`🔍 Testing ${endpoint.name}...`);
      const result = await testEndpoint(endpoint.url);
      
      if (result.success) {
        console.log(`✅ ${endpoint.name}: OK (${result.status})`);
        successCount++;
      } else {
        console.log(`❌ ${endpoint.name}: ${result.error}`);
      }
    }
    
    console.log(`\n📈 Success Rate: ${successCount}/${endpoints.length} (${Math.round(successCount/endpoints.length*100)}%)`);
    
    status.verify = successCount >= 3; // At least 3 endpoints should work
    return status.verify;
  } catch (error) {
    console.log('❌ Verification failed:', error.message);
    return false;
  }
}

// Show logs for debugging
async function showLogs() {
  console.log('\n📋 Recent Container Logs:');
  console.log('=========================\n');
  
  try {
    const deploymentPath = path.join(process.cwd(), 'deployment');
    const services = ['backend', 'merchant-portal', 'admin-dashboard', 'nginx'];
    
    for (const service of services) {
      console.log(`📋 ${service.toUpperCase()} Logs:`);
      try {
        const logsResult = await runCommand('docker-compose', ['-f', 'docker-compose-core.yml', 'logs', '--tail=10', service], deploymentPath, { silent: true });
        console.log(logsResult.stdout || 'No logs available');
      } catch (error) {
        console.log('Could not retrieve logs');
      }
      console.log('');
    }
  } catch (error) {
    console.log('Could not show logs:', error.message);
  }
}

// Generate final report
function generateReport() {
  console.log('\n📊 Deployment Report');
  console.log('====================\n');
  
  console.log('🎯 Status Summary:');
  Object.entries(status).forEach(([step, success]) => {
    const icon = success ? '✅' : '❌';
    console.log(`   ${step}: ${icon}`);
  });
  
  const allSuccess = Object.values(status).every(s => s);
  
  if (allSuccess) {
    console.log('\n🎉 Deployment Successful!');
    console.log('\n🌐 Service URLs:');
    console.log('================');
    console.log('🔧 Backend API:      http://localhost:5000');
    console.log('🏪 Merchant Portal:  http://localhost:3000');
    console.log('👑 Admin Dashboard:  http://localhost:3001');
    console.log('🌐 Nginx Proxy:      http://localhost');
    
    console.log('\n🔑 Test Commands:');
    console.log('=================');
    console.log('curl http://localhost:5000/health');
    console.log('curl http://localhost:5000/api/categories');
    console.log('curl http://localhost/health');
    
    console.log('\n🛠️ Management:');
    console.log('===============');
    console.log('📊 Status: docker-compose -f deployment/docker-compose-core.yml ps');
    console.log('📋 Logs:   docker-compose -f deployment/docker-compose-core.yml logs -f');
    console.log('🛑 Stop:   docker-compose -f deployment/docker-compose-core.yml down');
    
  } else {
    console.log('\n⚠️ Deployment had issues');
    console.log('\n🔧 Troubleshooting:');
    console.log('===================');
    if (!status.dockerCheck) {
      console.log('1. Start Docker Desktop');
    }
    if (!status.build) {
      console.log('2. Check build logs above');
    }
    if (!status.deploy) {
      console.log('3. Check deployment logs');
    }
    if (!status.verify) {
      console.log('4. Wait longer for services to start');
      console.log('5. Check container logs');
    }
  }
}

// Main deployment function
async function main() {
  try {
    const dockerOk = await checkDocker();
    if (!dockerOk) {
      console.log('\n🛑 Cannot proceed without Docker');
      return;
    }
    
    await cleanup();
    
    const buildOk = await buildImages();
    if (!buildOk) {
      console.log('\n🛑 Build failed, cannot proceed');
      await showLogs();
      return;
    }
    
    const deployOk = await deployServices();
    if (!deployOk) {
      console.log('\n🛑 Deployment failed');
      await showLogs();
      return;
    }
    
    await verifyDeployment();
    
    if (!status.verify) {
      await showLogs();
    }
    
    generateReport();
    
  } catch (error) {
    console.error('\n💥 Deployment failed:', error.message);
    process.exit(1);
  }
}

// Run deployment
main();
