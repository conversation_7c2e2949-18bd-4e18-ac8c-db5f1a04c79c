#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const http = require('http');

console.log('🔍 PromoTun Docker Connectivity Diagnosis');
console.log('=========================================\n');

// Diagnostic results
const diagnostics = {
  docker: { available: false, running: false },
  containers: {},
  networks: {},
  ports: {},
  services: {},
  logs: {}
};

// Utility function to run commands
function runCommand(command, args, cwd = process.cwd()) {
  return new Promise((resolve, reject) => {
    const process = spawn(command, args, {
      cwd,
      stdio: 'pipe',
      shell: true
    });

    let stdout = '';
    let stderr = '';

    process.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    process.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    process.on('close', (code) => {
      resolve({ code, stdout, stderr });
    });

    process.on('error', (error) => {
      reject({ error: error.message });
    });
  });
}

// Test HTTP endpoint
function testEndpoint(url, timeout = 5000) {
  return new Promise((resolve) => {
    const request = http.get(url, { timeout }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          success: true,
          status: res.statusCode,
          data: data.substring(0, 200)
        });
      });
    });

    request.on('error', (error) => {
      resolve({
        success: false,
        error: error.message
      });
    });

    request.on('timeout', () => {
      request.destroy();
      resolve({
        success: false,
        error: 'Request timeout'
      });
    });
  });
}

// Check Docker availability
async function checkDocker() {
  console.log('🐳 Checking Docker Status...\n');
  
  try {
    // Check Docker installation
    const dockerVersion = await runCommand('docker', ['--version']);
    if (dockerVersion.code === 0) {
      console.log('✅ Docker installed:', dockerVersion.stdout.trim());
      diagnostics.docker.available = true;
    } else {
      console.log('❌ Docker not installed');
      return false;
    }

    // Check Docker daemon
    const dockerInfo = await runCommand('docker', ['info']);
    if (dockerInfo.code === 0) {
      console.log('✅ Docker daemon running');
      diagnostics.docker.running = true;
      return true;
    } else {
      console.log('❌ Docker daemon not running');
      console.log('   Error:', dockerInfo.stderr.split('\n')[0]);
      return false;
    }
  } catch (error) {
    console.log('❌ Docker check failed:', error.message);
    return false;
  }
}

// Check container status
async function checkContainers() {
  console.log('\n📦 Checking Container Status...\n');
  
  try {
    const deploymentPath = path.join(process.cwd(), 'deployment');
    
    // Check if docker-compose.yml exists
    if (!fs.existsSync(path.join(deploymentPath, 'docker-compose.yml'))) {
      console.log('❌ docker-compose.yml not found in deployment directory');
      return false;
    }

    // Get container status
    const psResult = await runCommand('docker-compose', ['ps'], deploymentPath);
    if (psResult.code === 0) {
      console.log('📊 Container Status:');
      console.log(psResult.stdout);
      
      // Parse container status
      const lines = psResult.stdout.split('\n').slice(2); // Skip header
      lines.forEach(line => {
        if (line.trim()) {
          const parts = line.split(/\s+/);
          if (parts.length >= 4) {
            const containerName = parts[0];
            const status = parts[3];
            diagnostics.containers[containerName] = {
              status: status,
              running: status.includes('Up')
            };
          }
        }
      });
    } else {
      console.log('❌ Failed to get container status');
      console.log('   Error:', psResult.stderr);
    }

    // Check individual containers
    const expectedContainers = [
      'promotun-postgres',
      'promotun-redis', 
      'promotun-backend',
      'promotun-merchant-portal',
      'promotun-admin-dashboard',
      'promotun-nginx'
    ];

    for (const container of expectedContainers) {
      try {
        const inspectResult = await runCommand('docker', ['inspect', container]);
        if (inspectResult.code === 0) {
          const info = JSON.parse(inspectResult.stdout)[0];
          const state = info.State;
          
          console.log(`📦 ${container}:`);
          console.log(`   Status: ${state.Status}`);
          console.log(`   Running: ${state.Running}`);
          console.log(`   Health: ${state.Health?.Status || 'N/A'}`);
          
          diagnostics.containers[container] = {
            status: state.Status,
            running: state.Running,
            health: state.Health?.Status,
            exitCode: state.ExitCode
          };
        } else {
          console.log(`❌ ${container}: Not found`);
          diagnostics.containers[container] = { status: 'not_found' };
        }
      } catch (error) {
        console.log(`❌ ${container}: Error checking - ${error.message}`);
      }
    }

    return true;
  } catch (error) {
    console.log('❌ Container check failed:', error.message);
    return false;
  }
}

// Check network connectivity
async function checkNetworks() {
  console.log('\n🌐 Checking Network Connectivity...\n');
  
  try {
    // Check Docker networks
    const networksResult = await runCommand('docker', ['network', 'ls']);
    if (networksResult.code === 0) {
      console.log('📊 Docker Networks:');
      console.log(networksResult.stdout);
      
      // Check specific network
      const networkInspect = await runCommand('docker', ['network', 'inspect', 'promotun-network']);
      if (networkInspect.code === 0) {
        const networkInfo = JSON.parse(networkInspect.stdout)[0];
        console.log('\n🔍 PromoTun Network Details:');
        console.log(`   Name: ${networkInfo.Name}`);
        console.log(`   Driver: ${networkInfo.Driver}`);
        console.log(`   Containers: ${Object.keys(networkInfo.Containers || {}).length}`);
        
        diagnostics.networks['promotun-network'] = {
          exists: true,
          driver: networkInfo.Driver,
          containers: Object.keys(networkInfo.Containers || {})
        };
      } else {
        console.log('❌ PromoTun network not found');
        diagnostics.networks['promotun-network'] = { exists: false };
      }
    }

    return true;
  } catch (error) {
    console.log('❌ Network check failed:', error.message);
    return false;
  }
}

// Check port availability
async function checkPorts() {
  console.log('\n🔌 Checking Port Availability...\n');
  
  const ports = [80, 3000, 3001, 5000, 5432, 6379, 9090, 3002];
  
  for (const port of ports) {
    try {
      const netstatResult = await runCommand('netstat', ['-an']);
      const isInUse = netstatResult.stdout.includes(`:${port} `) || netstatResult.stdout.includes(`:${port}\t`);
      
      console.log(`🔌 Port ${port}: ${isInUse ? '❌ In Use' : '✅ Available'}`);
      diagnostics.ports[port] = { available: !isInUse };
    } catch (error) {
      console.log(`🔌 Port ${port}: ❓ Could not check`);
      diagnostics.ports[port] = { available: 'unknown' };
    }
  }

  return true;
}

// Test service endpoints
async function testServices() {
  console.log('\n🏥 Testing Service Endpoints...\n');
  
  const endpoints = [
    { name: 'Nginx Proxy', url: 'http://localhost/health' },
    { name: 'Backend API', url: 'http://localhost:5000/health' },
    { name: 'Merchant Portal', url: 'http://localhost:3000/health' },
    { name: 'Admin Dashboard', url: 'http://localhost:3001/health' },
    { name: 'Grafana', url: 'http://localhost:3002' },
    { name: 'Prometheus', url: 'http://localhost:9090' }
  ];

  for (const endpoint of endpoints) {
    console.log(`🔍 Testing ${endpoint.name}...`);
    const result = await testEndpoint(endpoint.url);
    
    if (result.success) {
      console.log(`✅ ${endpoint.name}: Responding (${result.status})`);
      diagnostics.services[endpoint.name] = {
        accessible: true,
        status: result.status,
        response: result.data.substring(0, 100)
      };
    } else {
      console.log(`❌ ${endpoint.name}: ${result.error}`);
      diagnostics.services[endpoint.name] = {
        accessible: false,
        error: result.error
      };
    }
  }

  return true;
}

// Check container logs
async function checkLogs() {
  console.log('\n📋 Checking Container Logs...\n');
  
  const containers = ['backend', 'merchant-portal', 'admin-dashboard', 'nginx'];
  const deploymentPath = path.join(process.cwd(), 'deployment');
  
  for (const container of containers) {
    try {
      console.log(`📋 ${container.toUpperCase()} Logs (last 10 lines):`);
      const logsResult = await runCommand('docker-compose', ['logs', '--tail=10', container], deploymentPath);
      
      if (logsResult.code === 0) {
        const logs = logsResult.stdout.trim();
        console.log(logs || '   No logs available');
        diagnostics.logs[container] = logs.split('\n').slice(-5); // Last 5 lines
      } else {
        console.log('   Could not retrieve logs');
        diagnostics.logs[container] = ['Could not retrieve logs'];
      }
      console.log('');
    } catch (error) {
      console.log(`   Error getting logs: ${error.message}\n`);
    }
  }

  return true;
}

// Generate diagnostic report
function generateReport() {
  console.log('\n📊 Diagnostic Report Summary');
  console.log('============================\n');
  
  // Docker status
  console.log('🐳 Docker Status:');
  console.log(`   Installed: ${diagnostics.docker.available ? '✅' : '❌'}`);
  console.log(`   Running: ${diagnostics.docker.running ? '✅' : '❌'}`);
  
  // Container status
  console.log('\n📦 Container Status:');
  Object.entries(diagnostics.containers).forEach(([name, info]) => {
    const status = info.running ? '✅ Running' : `❌ ${info.status}`;
    console.log(`   ${name}: ${status}`);
  });
  
  // Service accessibility
  console.log('\n🏥 Service Accessibility:');
  Object.entries(diagnostics.services).forEach(([name, info]) => {
    const status = info.accessible ? '✅ Accessible' : `❌ ${info.error}`;
    console.log(`   ${name}: ${status}`);
  });
  
  // Port status
  console.log('\n🔌 Port Status:');
  Object.entries(diagnostics.ports).forEach(([port, info]) => {
    const status = info.available === true ? '✅ Available' : 
                   info.available === false ? '❌ In Use' : '❓ Unknown';
    console.log(`   Port ${port}: ${status}`);
  });

  // Recommendations
  console.log('\n💡 Recommendations:');
  console.log('===================');
  
  if (!diagnostics.docker.running) {
    console.log('1. 🚀 Start Docker Desktop');
  }
  
  const runningContainers = Object.values(diagnostics.containers).filter(c => c.running).length;
  if (runningContainers === 0) {
    console.log('2. 🐳 Deploy containers: docker-compose up -d');
  }
  
  const accessibleServices = Object.values(diagnostics.services).filter(s => s.accessible).length;
  if (accessibleServices === 0) {
    console.log('3. ⏳ Wait for services to start (may take 2-3 minutes)');
  }
  
  const portsInUse = Object.entries(diagnostics.ports).filter(([_, info]) => info.available === false);
  if (portsInUse.length > 0) {
    console.log('4. 🔌 Free up ports:', portsInUse.map(([port]) => port).join(', '));
  }
}

// Main diagnostic function
async function runDiagnostics() {
  try {
    const dockerOk = await checkDocker();
    
    if (dockerOk) {
      await checkContainers();
      await checkNetworks();
      await checkPorts();
      await testServices();
      await checkLogs();
    }
    
    generateReport();
    
    console.log('\n🎯 Diagnosis completed!');
    
  } catch (error) {
    console.error('💥 Diagnosis failed:', error.message);
    process.exit(1);
  }
}

// Run diagnostics
runDiagnostics();
