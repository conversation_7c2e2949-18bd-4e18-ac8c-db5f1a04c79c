const redis = require('redis');
const logger = require('../utils/logger');

let client;

// Mock Redis client for development
function createMockRedisClient() {
  const mockStore = new Map();

  return {
    setEx: async (key, seconds, value) => {
      mockStore.set(key, { value, expires: Date.now() + (seconds * 1000) });
      logger.debug(`Mock Redis SET: ${key}`);
      return 'OK';
    },
    get: async (key) => {
      const item = mockStore.get(key);
      if (!item) return null;

      if (item.expires && Date.now() > item.expires) {
        mockStore.delete(key);
        return null;
      }

      logger.debug(`Mock Redis GET: ${key}`);
      return item.value;
    },
    del: async (key) => {
      const deleted = mockStore.delete(key);
      logger.debug(`Mock Redis DEL: ${key}`);
      return deleted ? 1 : 0;
    },
    keys: async (pattern) => {
      const keys = Array.from(mockStore.keys());
      logger.debug(`Mock Redis KEYS: ${pattern}`);
      return keys.filter(key => key.includes(pattern.replace('*', '')));
    },
    quit: async () => {
      mockStore.clear();
      logger.info('Mock Redis connection closed');
    }
  };
}

const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: process.env.REDIS_DB || 0,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: null,
};

async function connectRedis() {
  try {
    // In development mode without Redis, use mock connection
    if (process.env.NODE_ENV === 'development' && process.env.MOCK_EXTERNAL_APIS === 'true') {
      logger.warn('Using mock Redis connection for development');
      client = createMockRedisClient();
      return client;
    }

    client = redis.createClient({
      socket: {
        host: redisConfig.host,
        port: redisConfig.port,
      },
      password: redisConfig.password,
      database: redisConfig.db,
    });

    client.on('error', (err) => {
      logger.error('Redis Client Error:', err);
    });

    client.on('connect', () => {
      logger.info('Redis client connected');
    });

    client.on('ready', () => {
      logger.info('Redis client ready');
    });

    await client.connect();
    return client;
  } catch (error) {
    logger.error('Redis connection failed:', error);

    // Fallback to mock in development
    if (process.env.NODE_ENV === 'development') {
      logger.warn('Falling back to mock Redis for development');
      client = createMockRedisClient();
      return client;
    }

    throw error;
  }
}

function getRedisClient() {
  if (!client) {
    throw new Error('Redis not connected. Call connectRedis() first.');
  }
  return client;
}

// Cache helper functions
async function setCache(key, value, expireInSeconds = 3600) {
  try {
    const serializedValue = JSON.stringify(value);
    await client.setEx(key, expireInSeconds, serializedValue);
  } catch (error) {
    logger.error('Redis set error:', error);
    throw error;
  }
}

async function getCache(key) {
  try {
    const value = await client.get(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    logger.error('Redis get error:', error);
    return null;
  }
}

async function deleteCache(key) {
  try {
    await client.del(key);
  } catch (error) {
    logger.error('Redis delete error:', error);
    throw error;
  }
}

async function deleteCachePattern(pattern) {
  try {
    const keys = await client.keys(pattern);
    if (keys.length > 0) {
      await client.del(keys);
    }
  } catch (error) {
    logger.error('Redis delete pattern error:', error);
    throw error;
  }
}

async function closeRedis() {
  if (client) {
    await client.quit();
    logger.info('Redis connection closed');
  }
}

module.exports = {
  connectRedis,
  getRedisClient,
  setCache,
  getCache,
  deleteCache,
  deleteCachePattern,
  closeRedis
};
