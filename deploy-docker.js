#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🐳 PromoTun Docker Deployment');
console.log('=============================\n');

// Deployment status tracking
const deploymentStatus = {
  prerequisites: false,
  cleanup: false,
  build: false,
  deploy: false,
  healthCheck: false,
  verification: false
};

// Utility function to run commands
function runCommand(command, args, cwd, options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`📋 Running: ${command} ${args.join(' ')}`);
    console.log(`📁 Directory: ${cwd}\n`);
    
    const process = spawn(command, args, {
      cwd,
      stdio: options.silent ? 'pipe' : 'inherit',
      shell: true,
      ...options
    });

    let stdout = '';
    let stderr = '';

    if (options.silent) {
      process.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      process.stderr.on('data', (data) => {
        stderr += data.toString();
      });
    }

    process.on('close', (code) => {
      if (code === 0) {
        resolve({ code, stdout, stderr });
      } else {
        reject({ code, stdout, stderr });
      }
    });

    process.on('error', (error) => {
      reject({ error: error.message, code: -1 });
    });
  });
}

// Check prerequisites
async function checkPrerequisites() {
  console.log('🔍 Checking Prerequisites...\n');
  
  try {
    await runCommand('docker', ['--version'], process.cwd(), { silent: true });
    console.log('✅ Docker: Available');
    
    await runCommand('docker-compose', ['--version'], process.cwd(), { silent: true });
    console.log('✅ Docker Compose: Available');
    
    deploymentStatus.prerequisites = true;
    console.log('\n✅ Prerequisites check completed\n');
  } catch (error) {
    console.log('❌ Missing required tools. Please install Docker and Docker Compose.');
    throw new Error('Prerequisites not met');
  }
}

// Cleanup existing containers
async function cleanupExisting() {
  console.log('🧹 Cleaning up existing containers...\n');
  
  try {
    const deploymentPath = path.join(process.cwd(), 'deployment');
    
    // Stop and remove existing containers
    try {
      await runCommand('docker-compose', ['down', '-v'], deploymentPath, { silent: true });
      console.log('✅ Stopped existing containers');
    } catch (error) {
      console.log('ℹ️ No existing containers to stop');
    }

    // Remove unused images and volumes
    try {
      await runCommand('docker', ['system', 'prune', '-f'], process.cwd(), { silent: true });
      console.log('✅ Cleaned up unused Docker resources');
    } catch (error) {
      console.log('⚠️ Could not clean up Docker resources');
    }

    deploymentStatus.cleanup = true;
    console.log('\n✅ Cleanup completed\n');
  } catch (error) {
    console.log('⚠️ Cleanup had some issues, continuing...\n');
    deploymentStatus.cleanup = true;
  }
}

// Build Docker images
async function buildImages() {
  console.log('🔨 Building Docker images...\n');
  
  try {
    const deploymentPath = path.join(process.cwd(), 'deployment');
    
    // Build all images
    await runCommand('docker-compose', ['build', '--no-cache'], deploymentPath);
    
    deploymentStatus.build = true;
    console.log('\n✅ Docker images built successfully\n');
  } catch (error) {
    console.log('❌ Failed to build Docker images');
    throw error;
  }
}

// Deploy the stack
async function deployStack() {
  console.log('🚀 Deploying PromoTun stack...\n');
  
  try {
    const deploymentPath = path.join(process.cwd(), 'deployment');
    
    // Start all services
    await runCommand('docker-compose', ['up', '-d'], deploymentPath);
    
    deploymentStatus.deploy = true;
    console.log('\n✅ PromoTun stack deployed successfully\n');
  } catch (error) {
    console.log('❌ Failed to deploy stack');
    throw error;
  }
}

// Wait for services to be ready
async function waitForServices() {
  console.log('⏳ Waiting for services to be ready...\n');
  
  const services = [
    { name: 'PostgreSQL', url: 'postgres', timeout: 60 },
    { name: 'Redis', url: 'redis', timeout: 30 },
    { name: 'Backend API', url: 'backend', timeout: 90 },
    { name: 'Merchant Portal', url: 'merchant-portal', timeout: 60 },
    { name: 'Admin Dashboard', url: 'admin-dashboard', timeout: 60 }
  ];

  for (const service of services) {
    console.log(`⏳ Waiting for ${service.name}...`);
    
    let retries = service.timeout / 5; // Check every 5 seconds
    let ready = false;
    
    while (retries > 0 && !ready) {
      try {
        const deploymentPath = path.join(process.cwd(), 'deployment');
        const result = await runCommand('docker-compose', ['ps', service.url], deploymentPath, { silent: true });
        
        if (result.stdout.includes('Up') || result.stdout.includes('running')) {
          console.log(`✅ ${service.name}: Ready`);
          ready = true;
        } else {
          await new Promise(resolve => setTimeout(resolve, 5000));
          retries--;
        }
      } catch (error) {
        await new Promise(resolve => setTimeout(resolve, 5000));
        retries--;
      }
    }
    
    if (!ready) {
      console.log(`⚠️ ${service.name}: Timeout waiting for service`);
    }
  }
  
  console.log('\n✅ Service startup completed\n');
}

// Health check all services
async function healthCheckServices() {
  console.log('🏥 Running health checks...\n');
  
  const healthChecks = [
    { name: 'Backend API', url: 'http://localhost:5000/health' },
    { name: 'Merchant Portal', url: 'http://localhost:3000/health' },
    { name: 'Admin Dashboard', url: 'http://localhost:3001/health' },
    { name: 'Nginx Proxy', url: 'http://localhost/health' }
  ];

  let allHealthy = true;

  for (const check of healthChecks) {
    try {
      console.log(`🔍 Checking ${check.name}...`);
      
      // Use curl to check health endpoints
      await runCommand('curl', ['-f', '-s', check.url], process.cwd(), { silent: true });
      console.log(`✅ ${check.name}: Healthy`);
    } catch (error) {
      console.log(`❌ ${check.name}: Health check failed`);
      allHealthy = false;
    }
  }

  deploymentStatus.healthCheck = allHealthy;
  console.log(`\n${allHealthy ? '✅' : '⚠️'} Health checks completed\n`);
}

// Verify deployment
async function verifyDeployment() {
  console.log('🔍 Verifying deployment...\n');
  
  try {
    const deploymentPath = path.join(process.cwd(), 'deployment');
    
    // Check container status
    const result = await runCommand('docker-compose', ['ps'], deploymentPath, { silent: true });
    console.log('📊 Container Status:');
    console.log('===================');
    console.log(result.stdout);

    // Check logs for any errors
    console.log('\n📋 Recent Logs:');
    console.log('===============');
    
    const services = ['postgres', 'redis', 'backend', 'merchant-portal', 'admin-dashboard', 'nginx'];
    
    for (const service of services) {
      try {
        const logs = await runCommand('docker-compose', ['logs', '--tail=5', service], deploymentPath, { silent: true });
        console.log(`\n${service.toUpperCase()}:`);
        console.log(logs.stdout.split('\n').slice(-3).join('\n'));
      } catch (error) {
        console.log(`\n${service.toUpperCase()}: No logs available`);
      }
    }

    deploymentStatus.verification = true;
    console.log('\n✅ Deployment verification completed\n');
  } catch (error) {
    console.log('⚠️ Deployment verification had issues\n');
    deploymentStatus.verification = false;
  }
}

// Generate deployment report
function generateDeploymentReport() {
  console.log('📋 Docker Deployment Report');
  console.log('============================\n');
  
  console.log('🎯 Deployment Status:');
  Object.entries(deploymentStatus).forEach(([component, status]) => {
    const icon = status ? '✅' : '❌';
    const statusText = status ? 'SUCCESS' : 'FAILED';
    console.log(`${icon} ${component}: ${statusText}`);
  });

  console.log('\n🌐 Service URLs:');
  console.log('================');
  console.log('🔧 Backend API: http://localhost:5000');
  console.log('🏪 Merchant Portal: http://localhost:3000');
  console.log('👑 Admin Dashboard: http://localhost:3001');
  console.log('🌐 Nginx Proxy: http://localhost');
  console.log('📊 Grafana: http://localhost:3002');
  console.log('📈 Prometheus: http://localhost:9090');

  console.log('\n🐳 Docker Commands:');
  console.log('===================');
  console.log('📊 View status: docker-compose -f deployment/docker-compose.yml ps');
  console.log('📋 View logs: docker-compose -f deployment/docker-compose.yml logs -f');
  console.log('🛑 Stop services: docker-compose -f deployment/docker-compose.yml down');
  console.log('🔄 Restart: docker-compose -f deployment/docker-compose.yml restart');

  console.log('\n🔑 Test Credentials:');
  console.log('====================');
  console.log('👤 Consumer: <EMAIL> / password');
  console.log('🏪 Merchant: <EMAIL> / password');
  console.log('👑 Admin: <EMAIL> / password');

  console.log('\n📖 Next Steps:');
  console.log('==============');
  console.log('1. Test API endpoints: curl http://localhost:5000/health');
  console.log('2. Access merchant portal: http://localhost:3000');
  console.log('3. Access admin dashboard: http://localhost:3001');
  console.log('4. Monitor with Grafana: http://localhost:3002');
  console.log('5. Check container logs for any issues');

  const allSuccess = Object.values(deploymentStatus).every(status => status);
  
  if (allSuccess) {
    console.log('\n🎉 PromoTun Docker deployment completed successfully!');
    console.log('🚀 All services are running and ready for use!');
  } else {
    console.log('\n⚠️ Deployment completed with some issues.');
    console.log('🔧 Check the logs and status above for troubleshooting.');
  }
}

// Main deployment function
async function deployDocker() {
  try {
    await checkPrerequisites();
    await cleanupExisting();
    await buildImages();
    await deployStack();
    await waitForServices();
    await healthCheckServices();
    await verifyDeployment();
    generateDeploymentReport();
    
    console.log('\n🎯 Docker deployment completed!');
    process.exit(0);
    
  } catch (error) {
    console.error('💥 Docker deployment failed:', error.message);
    console.log('\n🔧 Troubleshooting Tips:');
    console.log('========================');
    console.log('1. Ensure Docker and Docker Compose are installed');
    console.log('2. Check that ports 80, 3000, 3001, 5000, 5432, 6379 are available');
    console.log('3. Verify environment variables in deployment/.env');
    console.log('4. Check Docker logs: docker-compose -f deployment/docker-compose.yml logs');
    console.log('5. Try cleaning up: docker system prune -a');
    process.exit(1);
  }
}

// Run the deployment
deployDocker();
