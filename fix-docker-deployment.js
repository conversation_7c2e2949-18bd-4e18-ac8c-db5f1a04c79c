#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔧 PromoTun Docker Deployment Fix');
console.log('=================================\n');

// Fix status tracking
const fixes = {
  dockerCheck: false,
  portCheck: false,
  configFix: false,
  networkFix: false,
  deployment: false,
  verification: false
};

// Utility function to run commands
function runCommand(command, args, cwd = process.cwd(), options = {}) {
  return new Promise((resolve, reject) => {
    if (!options.silent) {
      console.log(`📋 Running: ${command} ${args.join(' ')}`);
    }
    
    const process = spawn(command, args, {
      cwd,
      stdio: options.silent ? 'pipe' : 'inherit',
      shell: true
    });

    let stdout = '';
    let stderr = '';

    if (options.silent) {
      process.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      process.stderr.on('data', (data) => {
        stderr += data.toString();
      });
    }

    process.on('close', (code) => {
      if (code === 0) {
        resolve({ code, stdout, stderr });
      } else {
        reject({ code, stdout, stderr });
      }
    });

    process.on('error', (error) => {
      reject({ error: error.message });
    });
  });
}

// Check and fix Docker status
async function checkAndFixDocker() {
  console.log('🐳 Checking Docker Status...\n');
  
  try {
    // Check Docker installation
    await runCommand('docker', ['--version'], process.cwd(), { silent: true });
    console.log('✅ Docker is installed');
    
    // Check Docker daemon
    try {
      await runCommand('docker', ['info'], process.cwd(), { silent: true });
      console.log('✅ Docker daemon is running');
      fixes.dockerCheck = true;
      return true;
    } catch (error) {
      console.log('❌ Docker daemon is not running');
      console.log('💡 Please start Docker Desktop and wait for it to be ready');
      console.log('   Then run this script again\n');
      return false;
    }
  } catch (error) {
    console.log('❌ Docker is not installed');
    console.log('💡 Please install Docker Desktop from:');
    console.log('   https://www.docker.com/products/docker-desktop\n');
    return false;
  }
}

// Check and fix port conflicts
async function checkAndFixPorts() {
  console.log('🔌 Checking Port Availability...\n');
  
  const requiredPorts = [80, 3000, 3001, 5000, 5432, 6379];
  const conflicts = [];
  
  try {
    const netstatResult = await runCommand('netstat', ['-an'], process.cwd(), { silent: true });
    
    for (const port of requiredPorts) {
      const portInUse = netstatResult.stdout.includes(`:${port} `) || 
                       netstatResult.stdout.includes(`:${port}\t`);
      
      if (portInUse) {
        console.log(`❌ Port ${port}: In use`);
        conflicts.push(port);
      } else {
        console.log(`✅ Port ${port}: Available`);
      }
    }
    
    if (conflicts.length > 0) {
      console.log(`\n⚠️ Port conflicts detected: ${conflicts.join(', ')}`);
      console.log('💡 To fix port conflicts:');
      console.log('   1. Stop services using these ports');
      console.log('   2. Or modify docker-compose.yml to use different ports');
      console.log('   3. Check for existing Docker containers: docker ps\n');
      
      // Try to stop existing PromoTun containers
      try {
        const deploymentPath = path.join(process.cwd(), 'deployment');
        console.log('🛑 Attempting to stop existing PromoTun containers...');
        await runCommand('docker-compose', ['down'], deploymentPath, { silent: true });
        console.log('✅ Stopped existing containers\n');
      } catch (error) {
        console.log('ℹ️ No existing containers to stop\n');
      }
    }
    
    fixes.portCheck = true;
    return conflicts.length === 0;
  } catch (error) {
    console.log('⚠️ Could not check port availability\n');
    fixes.portCheck = true;
    return true; // Continue anyway
  }
}

// Fix configuration issues
async function fixConfigurations() {
  console.log('⚙️ Fixing Configuration Issues...\n');
  
  try {
    const deploymentPath = path.join(process.cwd(), 'deployment');
    
    // Check if deployment directory exists
    if (!fs.existsSync(deploymentPath)) {
      console.log('❌ Deployment directory not found');
      return false;
    }
    
    // Fix docker-compose.yml if needed
    const composePath = path.join(deploymentPath, 'docker-compose.yml');
    if (!fs.existsSync(composePath)) {
      console.log('❌ docker-compose.yml not found');
      return false;
    }
    
    console.log('✅ docker-compose.yml found');
    
    // Fix .env file
    const envPath = path.join(deploymentPath, '.env');
    if (!fs.existsSync(envPath)) {
      console.log('⚠️ .env file not found, creating default...');
      
      const defaultEnv = `# PromoTun Docker Environment
DB_PASSWORD=SecurePromoTunPass123!
POSTGRES_DB=promotun
POSTGRES_USER=postgres
POSTGRES_PASSWORD=SecurePromoTunPass123!
REDIS_PASSWORD=SecureRedisPass123!
JWT_SECRET=PromoTun-Super-Secret-JWT-Key-For-Production
NODE_ENV=production
API_URL=http://localhost/api
FRONTEND_URL=http://localhost
`;
      
      fs.writeFileSync(envPath, defaultEnv);
      console.log('✅ Created default .env file');
    } else {
      console.log('✅ .env file found');
    }
    
    // Check init-db.sql
    const initDbPath = path.join(deploymentPath, 'init-db.sql');
    if (!fs.existsSync(initDbPath)) {
      console.log('⚠️ init-db.sql not found');
    } else {
      console.log('✅ init-db.sql found');
    }
    
    // Fix backend .env for Docker
    const backendEnvPath = path.join(process.cwd(), 'backend', '.env');
    if (fs.existsSync(backendEnvPath)) {
      let backendEnv = fs.readFileSync(backendEnvPath, 'utf8');
      
      // Update for Docker containers
      backendEnv = backendEnv.replace(/DB_HOST=localhost/g, 'DB_HOST=postgres');
      backendEnv = backendEnv.replace(/REDIS_HOST=localhost/g, 'REDIS_HOST=redis');
      backendEnv = backendEnv.replace(/MOCK_DATABASE=true/g, 'MOCK_DATABASE=false');
      backendEnv = backendEnv.replace(/MOCK_EXTERNAL_APIS=true/g, 'MOCK_EXTERNAL_APIS=false');
      
      fs.writeFileSync(backendEnvPath, backendEnv);
      console.log('✅ Updated backend .env for Docker');
    }
    
    fixes.configFix = true;
    return true;
  } catch (error) {
    console.log('❌ Configuration fix failed:', error.message);
    return false;
  }
}

// Fix network issues
async function fixNetworkIssues() {
  console.log('🌐 Fixing Network Issues...\n');
  
  try {
    // Remove existing network if it exists
    try {
      await runCommand('docker', ['network', 'rm', 'promotun-network'], process.cwd(), { silent: true });
      console.log('🗑️ Removed existing network');
    } catch (error) {
      // Network doesn't exist, that's fine
    }
    
    // Clean up any orphaned containers
    try {
      await runCommand('docker', ['container', 'prune', '-f'], process.cwd(), { silent: true });
      console.log('🧹 Cleaned up orphaned containers');
    } catch (error) {
      // Ignore errors
    }
    
    fixes.networkFix = true;
    return true;
  } catch (error) {
    console.log('❌ Network fix failed:', error.message);
    return false;
  }
}

// Deploy with proper error handling
async function deployWithFixes() {
  console.log('🚀 Deploying PromoTun with Fixes...\n');
  
  try {
    const deploymentPath = path.join(process.cwd(), 'deployment');
    
    // Stop any existing containers
    console.log('🛑 Stopping existing containers...');
    try {
      await runCommand('docker-compose', ['down', '-v'], deploymentPath);
    } catch (error) {
      console.log('ℹ️ No existing containers to stop');
    }
    
    // Build images
    console.log('\n🔨 Building Docker images...');
    await runCommand('docker-compose', ['build', '--no-cache'], deploymentPath);
    
    // Start services in order
    console.log('\n🐳 Starting database services first...');
    await runCommand('docker-compose', ['up', '-d', 'postgres', 'redis'], deploymentPath);
    
    console.log('⏳ Waiting for database services to be ready...');
    await new Promise(resolve => setTimeout(resolve, 30000)); // Wait 30 seconds
    
    console.log('🔧 Starting backend service...');
    await runCommand('docker-compose', ['up', '-d', 'backend'], deploymentPath);
    
    console.log('⏳ Waiting for backend to be ready...');
    await new Promise(resolve => setTimeout(resolve, 20000)); // Wait 20 seconds
    
    console.log('🌐 Starting web services...');
    await runCommand('docker-compose', ['up', '-d', 'merchant-portal', 'admin-dashboard'], deploymentPath);
    
    console.log('⏳ Waiting for web services to be ready...');
    await new Promise(resolve => setTimeout(resolve, 30000)); // Wait 30 seconds
    
    console.log('🔀 Starting nginx proxy...');
    await runCommand('docker-compose', ['up', '-d', 'nginx'], deploymentPath);
    
    console.log('⏳ Final startup wait...');
    await new Promise(resolve => setTimeout(resolve, 15000)); // Wait 15 seconds
    
    fixes.deployment = true;
    return true;
  } catch (error) {
    console.log('❌ Deployment failed:', error.message);
    console.log('\n📋 Checking logs for errors...');
    
    try {
      const deploymentPath = path.join(process.cwd(), 'deployment');
      const logsResult = await runCommand('docker-compose', ['logs', '--tail=20'], deploymentPath, { silent: true });
      console.log('Recent logs:');
      console.log(logsResult.stdout);
    } catch (logError) {
      console.log('Could not retrieve logs');
    }
    
    return false;
  }
}

// Verify deployment
async function verifyDeployment() {
  console.log('🔍 Verifying Deployment...\n');
  
  try {
    const deploymentPath = path.join(process.cwd(), 'deployment');
    
    // Check container status
    const psResult = await runCommand('docker-compose', ['ps'], deploymentPath, { silent: true });
    console.log('📊 Container Status:');
    console.log(psResult.stdout);
    
    // Test endpoints
    const endpoints = [
      { name: 'Backend Health', url: 'http://localhost:5000/health' },
      { name: 'Nginx Proxy', url: 'http://localhost/health' }
    ];
    
    console.log('\n🏥 Testing Endpoints:');
    for (const endpoint of endpoints) {
      try {
        const testResult = await runCommand('curl', ['-f', '-s', endpoint.url], process.cwd(), { silent: true });
        if (testResult.code === 0) {
          console.log(`✅ ${endpoint.name}: Working`);
        } else {
          console.log(`❌ ${endpoint.name}: Not responding`);
        }
      } catch (error) {
        console.log(`❌ ${endpoint.name}: Error - ${error.message}`);
      }
    }
    
    fixes.verification = true;
    return true;
  } catch (error) {
    console.log('❌ Verification failed:', error.message);
    return false;
  }
}

// Generate fix report
function generateFixReport() {
  console.log('\n📊 Fix Report Summary');
  console.log('=====================\n');
  
  console.log('🔧 Fix Status:');
  Object.entries(fixes).forEach(([fix, status]) => {
    const icon = status ? '✅' : '❌';
    console.log(`   ${fix}: ${icon}`);
  });
  
  const allFixed = Object.values(fixes).every(status => status);
  
  if (allFixed) {
    console.log('\n🎉 All fixes applied successfully!');
    console.log('\n🌐 Service URLs:');
    console.log('================');
    console.log('🔧 Backend API:      http://localhost:5000');
    console.log('🏪 Merchant Portal:  http://localhost:3000');
    console.log('👑 Admin Dashboard:  http://localhost:3001');
    console.log('🌐 Main Application: http://localhost');
    
    console.log('\n🔑 Test Commands:');
    console.log('=================');
    console.log('curl http://localhost:5000/health');
    console.log('curl http://localhost/health');
    
  } else {
    console.log('\n⚠️ Some fixes failed. Please check the errors above.');
    console.log('\n🔧 Manual Steps:');
    console.log('================');
    if (!fixes.dockerCheck) {
      console.log('1. Start Docker Desktop');
    }
    if (!fixes.deployment) {
      console.log('2. Check Docker logs: docker-compose -f deployment/docker-compose.yml logs');
    }
  }
}

// Main fix function
async function runFixes() {
  try {
    const dockerOk = await checkAndFixDocker();
    if (!dockerOk) {
      console.log('🛑 Cannot proceed without Docker. Please start Docker Desktop first.');
      return;
    }
    
    await checkAndFixPorts();
    await fixConfigurations();
    await fixNetworkIssues();
    
    const deploymentOk = await deployWithFixes();
    if (deploymentOk) {
      await verifyDeployment();
    }
    
    generateFixReport();
    
    console.log('\n🎯 Fix process completed!');
    
  } catch (error) {
    console.error('💥 Fix process failed:', error.message);
    process.exit(1);
  }
}

// Run fixes
runFixes();
