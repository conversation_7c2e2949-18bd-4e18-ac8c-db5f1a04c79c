{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to initialize Firebase Admin SDK:\u001b[39m","timestamp":"2025-06-20 10:46:50:4650"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mPush notifications will be mocked in development mode\u001b[39m","timestamp":"2025-06-20 10:46:50:4650"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to initialize Firebase Admin SDK:\u001b[39m","timestamp":"2025-06-20 10:48:22:4822"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mPush notifications will be mocked in development mode\u001b[39m","timestamp":"2025-06-20 10:48:22:4822"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mUsing mock database connection for development\u001b[39m","timestamp":"2025-06-20 10:48:22:4822"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-06-20 10:48:22:4822"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mUsing mock Redis connection for development\u001b[39m","timestamp":"2025-06-20 10:48:22:4822"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-20 10:48:22:4822"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 5000\u001b[39m","timestamp":"2025-06-20 10:48:22:4822"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-20 10:48:22:4822"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:50:35 +0000] \"GET /health HTTP/1.1\" 200 75 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:50:35:5035"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: SELECT id FROM users WHERE email = $1...\u001b[39m","timestamp":"2025-06-20 10:50:35:5035"}
{"duration":15,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":1,"text":"SELECT id FROM users WHERE email = $1","timestamp":"2025-06-20 10:50:35:5035"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:50:35 +0000] \"POST /api/auth/register HTTP/1.1\" 409 65 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:50:35:5035"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: SELECT id, email, password_hash, first_name, last_name, user_type, preferred_language, is_verified, ...\u001b[39m","timestamp":"2025-06-20 10:50:35:5035"}
{"duration":2,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":1,"text":"SELECT id, email, password_hash, first_name, last_name, user_type, preferred_language, is_verified, ...","timestamp":"2025-06-20 10:50:35:5035"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:50:36 +0000] \"POST /api/auth/login HTTP/1.1\" 401 49 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:50:36:5036"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: \u001b[39m\n\u001b[37m      SELECT \u001b[39m\n\u001b[37m        id, name, name_fr, name_ar, description, description_fr, description_ar,\u001b[39m\n\u001b[37m    ...\u001b[39m","timestamp":"2025-06-20 10:50:36:5036"}
{"duration":6,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":0,"text":"\n      SELECT \n        id, name, name_fr, name_ar, description, description_fr, description_ar,\n    ...","timestamp":"2025-06-20 10:50:36:5036"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:50:36 +0000] \"GET /api/categories HTTP/1.1\" 200 41 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:50:36:5036"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: \u001b[39m\n\u001b[37m      SELECT \u001b[39m\n\u001b[37m        p.id, p.title, p.description, p.original_price, p.discounted_price,\u001b[39m\n\u001b[37m        p...\u001b[39m","timestamp":"2025-06-20 10:50:36:5036"}
{"duration":3,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":0,"text":"\n      SELECT \n        p.id, p.title, p.description, p.original_price, p.discounted_price,\n        p...","timestamp":"2025-06-20 10:50:36:5036"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: \u001b[39m\n\u001b[37m      SELECT COUNT(DISTINCT p.id)\u001b[39m\n\u001b[37m      FROM promotions p\u001b[39m\n\u001b[37m      LEFT JOIN business_locations bl ON ...\u001b[39m","timestamp":"2025-06-20 10:50:36:5036"}
{"duration":6,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":0,"text":"\n      SELECT COUNT(DISTINCT p.id)\n      FROM promotions p\n      LEFT JOIN business_locations bl ON ...","timestamp":"2025-06-20 10:50:36:5036"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mGet promotions error: Cannot read properties of undefined (reading 'count')\u001b[39m","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at K:\\AI\\PromoTun\\backend\\src\\routes\\promotions.js:131:48\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-20 10:50:36:5036"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:50:36 +0000] \"GET /api/promotions HTTP/1.1\" 500 51 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:50:36:5036"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:50:36 +0000] \"GET /api/nonexistent HTTP/1.1\" 404 45 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:50:36:5036"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:50:36 +0000] \"GET /health HTTP/1.1\" 200 75 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:50:36:5036"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to initialize Firebase Admin SDK:\u001b[39m","timestamp":"2025-06-20 10:51:42:5142"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mPush notifications will be mocked in development mode\u001b[39m","timestamp":"2025-06-20 10:51:42:5142"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mUsing mock database connection for development\u001b[39m","timestamp":"2025-06-20 10:51:42:5142"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-06-20 10:51:42:5142"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mUsing mock Redis connection for development\u001b[39m","timestamp":"2025-06-20 10:51:42:5142"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-20 10:51:42:5142"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 5000\u001b[39m","timestamp":"2025-06-20 10:51:42:5142"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-20 10:51:42:5142"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:51:53 +0000] \"GET /health HTTP/1.1\" 200 74 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:51:53:5153"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: SELECT id FROM users WHERE email = $1...\u001b[39m","timestamp":"2025-06-20 10:51:53:5153"}
{"duration":1,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":0,"text":"SELECT id FROM users WHERE email = $1","timestamp":"2025-06-20 10:51:53:5153"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: INSERT INTO users (email, password_hash, first_name, last_name, user_type, preferred_language)\u001b[39m\n\u001b[37m     ...\u001b[39m","timestamp":"2025-06-20 10:51:53:5153"}
{"duration":2,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":1,"text":"INSERT INTO users (email, password_hash, first_name, last_name, user_type, preferred_language)\n     ...","timestamp":"2025-06-20 10:51:53:5153"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: INSERT INTO user_profiles (user_id) VALUES ($1)...\u001b[39m","timestamp":"2025-06-20 10:51:53:5153"}
{"duration":0,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":0,"text":"INSERT INTO user_profiles (user_id) VALUES ($1)","timestamp":"2025-06-20 10:51:53:5153"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock Redis SET: verify_c9624c60e865cd53dd6c6a70262af0bd64267c065688e442885f6a39ad929947\u001b[39m","timestamp":"2025-06-20 10:51:53:5153"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mEmail sending failed: nodemailer.createTransporter is not a function\u001b[39m","stack":"TypeError: nodemailer.createTransporter is not a function\n    at createTransporter (K:\\AI\\PromoTun\\backend\\src\\services\\emailService.js:17:23)\n    at sendEmail (K:\\AI\\PromoTun\\backend\\src\\services\\emailService.js:33:25)\n    at K:\\AI\\PromoTun\\backend\\src\\routes\\auth.js:75:11","timestamp":"2025-06-20 10:51:53:5153"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRegistration error: nodemailer.createTransporter is not a function\u001b[39m","stack":"TypeError: nodemailer.createTransporter is not a function\n    at createTransporter (K:\\AI\\PromoTun\\backend\\src\\services\\emailService.js:17:23)\n    at sendEmail (K:\\AI\\PromoTun\\backend\\src\\services\\emailService.js:33:25)\n    at K:\\AI\\PromoTun\\backend\\src\\routes\\auth.js:75:11","timestamp":"2025-06-20 10:51:53:5153"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:51:53 +0000] \"POST /api/auth/register HTTP/1.1\" 500 51 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:51:53:5153"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: SELECT id, email, password_hash, first_name, last_name, user_type, preferred_language, is_verified, ...\u001b[39m","timestamp":"2025-06-20 10:51:53:5153"}
{"duration":6,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":0,"text":"SELECT id, email, password_hash, first_name, last_name, user_type, preferred_language, is_verified, ...","timestamp":"2025-06-20 10:51:53:5153"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:51:53 +0000] \"POST /api/auth/login HTTP/1.1\" 401 49 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:51:53:5153"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: \u001b[39m\n\u001b[37m      SELECT \u001b[39m\n\u001b[37m        id, name, name_fr, name_ar, description, description_fr, description_ar,\u001b[39m\n\u001b[37m    ...\u001b[39m","timestamp":"2025-06-20 10:51:53:5153"}
{"duration":9,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":3,"text":"\n      SELECT \n        id, name, name_fr, name_ar, description, description_fr, description_ar,\n    ...","timestamp":"2025-06-20 10:51:53:5153"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:51:53 +0000] \"GET /api/categories HTTP/1.1\" 200 324 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:51:53:5153"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: \u001b[39m\n\u001b[37m      SELECT \u001b[39m\n\u001b[37m        p.id, p.title, p.description, p.original_price, p.discounted_price,\u001b[39m\n\u001b[37m        p...\u001b[39m","timestamp":"2025-06-20 10:51:53:5153"}
{"duration":8,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":1,"text":"\n      SELECT \n        p.id, p.title, p.description, p.original_price, p.discounted_price,\n        p...","timestamp":"2025-06-20 10:51:53:5153"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: \u001b[39m\n\u001b[37m      SELECT COUNT(DISTINCT p.id)\u001b[39m\n\u001b[37m      FROM promotions p\u001b[39m\n\u001b[37m      LEFT JOIN business_locations bl ON ...\u001b[39m","timestamp":"2025-06-20 10:51:53:5153"}
{"duration":6,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":1,"text":"\n      SELECT COUNT(DISTINCT p.id)\n      FROM promotions p\n      LEFT JOIN business_locations bl ON ...","timestamp":"2025-06-20 10:51:53:5153"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:51:53 +0000] \"GET /api/promotions HTTP/1.1\" 200 435 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:51:53:5153"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:51:53 +0000] \"GET /api/nonexistent HTTP/1.1\" 404 45 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:51:53:5153"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:51:53 +0000] \"GET /health HTTP/1.1\" 200 74 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:51:53:5153"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:52:52 +0000] \"GET /health HTTP/1.1\" 200 73 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:52:52:5252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: \u001b[39m\n\u001b[37m      SELECT \u001b[39m\n\u001b[37m        id, name, name_fr, name_ar, description, description_fr, description_ar,\u001b[39m\n\u001b[37m    ...\u001b[39m","timestamp":"2025-06-20 10:52:52:5252"}
{"duration":2,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":3,"text":"\n      SELECT \n        id, name, name_fr, name_ar, description, description_fr, description_ar,\n    ...","timestamp":"2025-06-20 10:52:52:5252"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:52:52 +0000] \"GET /api/categories HTTP/1.1\" 200 324 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:52:52:5252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: \u001b[39m\n\u001b[37m      SELECT \u001b[39m\n\u001b[37m        p.id, p.title, p.description, p.original_price, p.discounted_price,\u001b[39m\n\u001b[37m        p...\u001b[39m","timestamp":"2025-06-20 10:52:52:5252"}
{"duration":2,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":1,"text":"\n      SELECT \n        p.id, p.title, p.description, p.original_price, p.discounted_price,\n        p...","timestamp":"2025-06-20 10:52:52:5252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: \u001b[39m\n\u001b[37m      SELECT COUNT(DISTINCT p.id)\u001b[39m\n\u001b[37m      FROM promotions p\u001b[39m\n\u001b[37m      LEFT JOIN business_locations bl ON ...\u001b[39m","timestamp":"2025-06-20 10:52:52:5252"}
{"duration":2,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":1,"text":"\n      SELECT COUNT(DISTINCT p.id)\n      FROM promotions p\n      LEFT JOIN business_locations bl ON ...","timestamp":"2025-06-20 10:52:52:5252"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:52:52 +0000] \"GET /api/promotions HTTP/1.1\" 200 435 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:52:52:5252"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:52:52 +0000] \"GET /api/users/profile HTTP/1.1\" 401 51 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:52:52:5252"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:52:53 +0000] \"GET /health HTTP/1.1\" 200 74 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:52:53:5253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:52:53 +0000] \"GET /api/nonexistent HTTP/1.1\" 404 45 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:52:53:5253"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError: Unexpected token '\"', \"\"invalid json\"\" is not valid JSON\u001b[39m","method":"POST","stack":"SyntaxError: Unexpected token '\"', \"\"invalid json\"\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (K:\\AI\\PromoTun\\backend\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (K:\\AI\\PromoTun\\backend\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at K:\\AI\\PromoTun\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (K:\\AI\\PromoTun\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (K:\\AI\\PromoTun\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (K:\\AI\\PromoTun\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)","timestamp":"2025-06-20 10:52:53:5253","url":"/api/auth/login"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: SELECT id, email, password_hash, first_name, last_name, user_type, preferred_language, is_verified, ...\u001b[39m","timestamp":"2025-06-20 10:52:53:5253"}
{"duration":10,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":1,"text":"SELECT id, email, password_hash, first_name, last_name, user_type, preferred_language, is_verified, ...","timestamp":"2025-06-20 10:52:53:5253"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: SELECT id, email, password_hash, first_name, last_name, user_type, preferred_language, is_verified, ...\u001b[39m","timestamp":"2025-06-20 10:52:53:5253"}
{"duration":2,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":1,"text":"SELECT id, email, password_hash, first_name, last_name, user_type, preferred_language, is_verified, ...","timestamp":"2025-06-20 10:52:53:5253"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: SELECT id, email, password_hash, first_name, last_name, user_type, preferred_language, is_verified, ...\u001b[39m","timestamp":"2025-06-20 10:52:53:5253"}
{"duration":0,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":1,"text":"SELECT id, email, password_hash, first_name, last_name, user_type, preferred_language, is_verified, ...","timestamp":"2025-06-20 10:52:53:5253"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: SELECT id, email, password_hash, first_name, last_name, user_type, preferred_language, is_verified, ...\u001b[39m","timestamp":"2025-06-20 10:52:53:5253"}
{"duration":1,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":1,"text":"SELECT id, email, password_hash, first_name, last_name, user_type, preferred_language, is_verified, ...","timestamp":"2025-06-20 10:52:53:5253"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: SELECT id, email, password_hash, first_name, last_name, user_type, preferred_language, is_verified, ...\u001b[39m","timestamp":"2025-06-20 10:52:53:5253"}
{"duration":2,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":1,"text":"SELECT id, email, password_hash, first_name, last_name, user_type, preferred_language, is_verified, ...","timestamp":"2025-06-20 10:52:53:5253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:52:56 +0000] \"POST /api/auth/login HTTP/1.1\" 401 49 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:52:56:5256"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:52:56 +0000] \"POST /api/auth/login HTTP/1.1\" 401 49 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:52:56:5256"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:52:56 +0000] \"POST /api/auth/login HTTP/1.1\" 401 49 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:52:56:5256"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:52:56 +0000] \"POST /api/auth/login HTTP/1.1\" 401 49 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:52:56:5256"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:52:56 +0000] \"POST /api/auth/login HTTP/1.1\" 401 49 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:52:56:5256"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mMock query: \u001b[39m\n\u001b[37m      SELECT \u001b[39m\n\u001b[37m        id, name, name_fr, name_ar, description, description_fr, description_ar,\u001b[39m\n\u001b[37m    ...\u001b[39m","timestamp":"2025-06-20 10:52:56:5256"}
{"duration":11,"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuted query\u001b[39m","rows":3,"text":"\n      SELECT \n        id, name, name_fr, name_ar, description, description_fr, description_ar,\n    ...","timestamp":"2025-06-20 10:52:56:5256"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [20/Jun/2025:09:52:56 +0000] \"GET /api/categories HTTP/1.1\" 200 324 \"-\" \"-\"\u001b[39m","timestamp":"2025-06-20 10:52:56:5256"}
