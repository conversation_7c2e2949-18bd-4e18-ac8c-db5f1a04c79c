const { Pool } = require('pg');
const logger = require('../utils/logger');

let pool;

// Mock database for development
function createMockPool() {
  return {
    connect: async () => ({
      query: async (text, params) => {
        logger.debug(`Mock query: ${text.substring(0, 100)}...`);

        // Mock responses for common queries
        if (text.includes('SELECT NOW()')) {
          return { rows: [{ now: new Date() }] };
        }

        if (text.includes('SELECT table_name FROM information_schema.tables')) {
          return { rows: [{ table_name: 'users' }, { table_name: 'promotions' }] };
        }

        if (text.includes('INSERT INTO users')) {
          return {
            rows: [{
              id: 'mock-user-id',
              email: params?.[0] || '<EMAIL>',
              first_name: params?.[2] || 'Mock',
              last_name: params?.[3] || 'User',
              user_type: params?.[4] || 'consumer',
              preferred_language: params?.[5] || 'en',
              is_verified: false,
              created_at: new Date()
            }],
            rowCount: 1
          };
        }

        if (text.includes('SELECT') && text.includes('FROM users WHERE email')) {
          return {
            rows: [{
              id: 'mock-user-id',
              email: '<EMAIL>',
              password_hash: '$2b$12$mockhashedpassword',
              first_name: 'Mock',
              last_name: 'User',
              user_type: 'consumer',
              preferred_language: 'en',
              is_verified: true,
              is_active: true
            }],
            rowCount: 1
          };
        }

        // Default mock response
        return { rows: [], rowCount: 0 };
      },
      release: () => {}
    }),
    query: async (text, params) => {
      const client = await this.connect();
      return client.query(text, params);
    },
    end: async () => {
      logger.info('Mock database connection closed');
    }
  };
}

const dbConfig = {
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'l192.168.100.14',
  database: process.env.DB_NAME || 'promotun',
  password: process.env.DB_PASSWORD || 'Tradigi**2023',
  port: process.env.DB_PORT || 5432,
  max: 20, // maximum number of clients in the pool
  idleTimeoutMillis: 30000, // how long a client is allowed to remain idle
  connectionTimeoutMillis: 2000, // how long to wait when connecting a new client
};

async function connectDatabase() {
  try {
    // In development mode without PostgreSQL, use mock connection
    if (process.env.NODE_ENV === 'development' && process.env.MOCK_DATABASE === 'true') {
      logger.warn('Using mock database connection for development');
      pool = createMockPool();
      return pool;
    }

    pool = new Pool(dbConfig);

    // Test the connection
    const client = await pool.connect();
    await client.query('SELECT NOW()');
    client.release();

    logger.info('Database connection established successfully');
    return pool;
  } catch (error) {
    logger.error('Database connection failed:', error);

    // Fallback to mock in development
    if (process.env.NODE_ENV === 'development') {
      logger.warn('Falling back to mock database for development');
      pool = createMockPool();
      return pool;
    }

    throw error;
  }
}

function getPool() {
  if (!pool) {
    throw new Error('Database not connected. Call connectDatabase() first.');
  }
  return pool;
}

async function query(text, params) {
  const client = await pool.connect();
  try {
    const start = Date.now();
    const result = await client.query(text, params);
    const duration = Date.now() - start;
    
    logger.debug('Executed query', {
      text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
      duration,
      rows: result.rowCount
    });
    
    return result;
  } catch (error) {
    logger.error('Database query error:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function transaction(callback) {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error('Transaction error:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function closeDatabase() {
  if (pool) {
    await pool.end();
    logger.info('Database connection closed');
  }
}

module.exports = {
  connectDatabase,
  getPool,
  query,
  transaction,
  closeDatabase
};
