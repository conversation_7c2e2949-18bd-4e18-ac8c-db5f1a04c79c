#!/usr/bin/env node

const http = require('http');

console.log('🧪 Testing PromoTun API Endpoints');
console.log('==================================\n');

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

// Utility function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = body ? JSON.parse(body) : {};
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: jsonBody
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Test function
async function runTest(name, testFn) {
  try {
    console.log(`🔍 Testing: ${name}`);
    const result = await testFn();
    
    if (result.success) {
      console.log(`✅ ${name}: PASSED`);
      if (result.details) {
        console.log(`   ${result.details}`);
      }
      testResults.passed++;
    } else {
      console.log(`❌ ${name}: FAILED`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
      testResults.failed++;
    }
    
    testResults.tests.push({ name, success: result.success, error: result.error });
    console.log('');
  } catch (error) {
    console.log(`❌ ${name}: ERROR - ${error.message}`);
    testResults.failed++;
    testResults.tests.push({ name, success: false, error: error.message });
    console.log('');
  }
}

// Test health endpoint
async function testHealthEndpoint() {
  const response = await makeRequest({
    hostname: 'localhost',
    port: 5000,
    path: '/health',
    method: 'GET'
  });

  if (response.statusCode === 200 && response.body.status === 'OK') {
    return {
      success: true,
      details: `Server uptime: ${response.body.uptime}s`
    };
  } else {
    return {
      success: false,
      error: `Expected 200 OK, got ${response.statusCode}`
    };
  }
}

// Test user registration
async function testUserRegistration() {
  const userData = {
    email: '<EMAIL>',
    password: 'TestPass123!',
    firstName: 'Test',
    lastName: 'User',
    userType: 'consumer',
    preferredLanguage: 'en'
  };

  const response = await makeRequest({
    hostname: 'localhost',
    port: 5000,
    path: '/api/auth/register',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  }, userData);

  if (response.statusCode === 201 && response.body.success) {
    return {
      success: true,
      details: `User registered with ID: ${response.body.data?.user?.id || 'mock-id'}`
    };
  } else {
    return {
      success: false,
      error: `Registration failed: ${response.body.message || 'Unknown error'}`
    };
  }
}

// Test user login
async function testUserLogin() {
  const loginData = {
    email: '<EMAIL>',
    password: 'TestPass123!'
  };

  const response = await makeRequest({
    hostname: 'localhost',
    port: 5000,
    path: '/api/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  }, loginData);

  if (response.statusCode === 200 && response.body.success) {
    return {
      success: true,
      details: `Login successful, token received`
    };
  } else {
    return {
      success: false,
      error: `Login failed: ${response.body.message || 'Unknown error'}`
    };
  }
}

// Test categories endpoint
async function testCategoriesEndpoint() {
  const response = await makeRequest({
    hostname: 'localhost',
    port: 5000,
    path: '/api/categories',
    method: 'GET'
  });

  if (response.statusCode === 200 && response.body.success) {
    return {
      success: true,
      details: `Categories retrieved successfully`
    };
  } else {
    return {
      success: false,
      error: `Categories request failed: ${response.body.message || 'Unknown error'}`
    };
  }
}

// Test promotions endpoint
async function testPromotionsEndpoint() {
  const response = await makeRequest({
    hostname: 'localhost',
    port: 5000,
    path: '/api/promotions',
    method: 'GET'
  });

  if (response.statusCode === 200 && response.body.success !== false) {
    return {
      success: true,
      details: `Promotions endpoint accessible`
    };
  } else {
    return {
      success: false,
      error: `Promotions request failed: ${response.body.message || 'Unknown error'}`
    };
  }
}

// Test invalid endpoint (404)
async function testInvalidEndpoint() {
  const response = await makeRequest({
    hostname: 'localhost',
    port: 5000,
    path: '/api/nonexistent',
    method: 'GET'
  });

  if (response.statusCode === 404) {
    return {
      success: true,
      details: `404 error handling working correctly`
    };
  } else {
    return {
      success: false,
      error: `Expected 404, got ${response.statusCode}`
    };
  }
}

// Test CORS headers
async function testCORSHeaders() {
  const response = await makeRequest({
    hostname: 'localhost',
    port: 5000,
    path: '/health',
    method: 'OPTIONS',
    headers: {
      'Origin': 'http://localhost:3000'
    }
  });

  if (response.headers['access-control-allow-origin']) {
    return {
      success: true,
      details: `CORS headers present`
    };
  } else {
    return {
      success: false,
      error: `CORS headers missing`
    };
  }
}

// Test security headers
async function testSecurityHeaders() {
  const response = await makeRequest({
    hostname: 'localhost',
    port: 5000,
    path: '/health',
    method: 'GET'
  });

  const securityHeaders = [
    'x-content-type-options',
    'x-frame-options',
    'x-xss-protection'
  ];

  const missingHeaders = securityHeaders.filter(header => !response.headers[header]);

  if (missingHeaders.length === 0) {
    return {
      success: true,
      details: `All security headers present`
    };
  } else {
    return {
      success: false,
      error: `Missing security headers: ${missingHeaders.join(', ')}`
    };
  }
}

// Main test runner
async function runAllTests() {
  console.log('Starting API endpoint tests...\n');

  await runTest('Health Endpoint', testHealthEndpoint);
  await runTest('User Registration', testUserRegistration);
  await runTest('User Login', testUserLogin);
  await runTest('Categories Endpoint', testCategoriesEndpoint);
  await runTest('Promotions Endpoint', testPromotionsEndpoint);
  await runTest('Invalid Endpoint (404)', testInvalidEndpoint);
  await runTest('CORS Headers', testCORSHeaders);
  await runTest('Security Headers', testSecurityHeaders);

  // Generate report
  console.log('📊 Test Results Summary');
  console.log('=======================');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);

  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.tests
      .filter(test => !test.success)
      .forEach(test => {
        console.log(`   - ${test.name}: ${test.error}`);
      });
  }

  console.log('\n🎯 API testing completed!');
  
  if (testResults.failed === 0) {
    console.log('🎉 All tests passed! API is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Check the errors above.');
  }
}

// Run the tests
runAllTests().catch(error => {
  console.error('💥 Test runner failed:', error.message);
  process.exit(1);
});
